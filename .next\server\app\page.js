/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2e5e0579d49b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFnaWMtYWNhZGVteS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MjMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJlNWUwNTc5ZDQ5YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Crimson_Text\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-crimson\",\"weight\":[\"400\",\"600\"]}],\"variableName\":\"crimson\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Crimson_Text\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-crimson\\\",\\\"weight\\\":[\\\"400\\\",\\\"600\\\"]}],\\\"variableName\\\":\\\"crimson\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Magic Academy - Professional Magic Tutorials\",\n    description: \"Learn advanced magic techniques from a professional magician. Exclusive tutorials, insights, and original card magic methods.\",\n    keywords: [\n        \"magic\",\n        \"tutorials\",\n        \"card magic\",\n        \"professional magic\",\n        \"magic lessons\"\n    ],\n    authors: [\n        {\n            name: \"Magic Academy\"\n        }\n    ],\n    openGraph: {\n        title: \"Magic Academy - Professional Magic Tutorials\",\n        description: \"Learn advanced magic techniques from a professional magician.\",\n        type: \"website\",\n        locale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cream-50 text-navy-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-cream-50/95 backdrop-blur-sm border-b-2 border-navy-200 shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-display font-semibold text-burgundy-800 tracking-wide relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gold-600 mr-2\",\n                                            children: \"❦\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 10,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Magic Academy\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-1 left-8 right-0 h-0.5 bg-gradient-to-r from-burgundy-600 to-transparent opacity-60\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 9,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 8,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#home\",\n                                        className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                        children: [\n                                            \"Home\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#portfolio\",\n                                        className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                        children: [\n                                            \"Portfolio\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#blog\",\n                                        className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                        children: [\n                                            \"Blog\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#tutorials\",\n                                        className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                        children: [\n                                            \"Tutorials\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/auth/login\",\n                                        className: \"btn-outline ml-4\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"home\",\n                className: \"hero-section pt-24 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 right-20 text-4xl text-gold-600 -rotate-12\",\n                                children: \"◆\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45\",\n                                children: \"✦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-max text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"classical-border mb-12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-display font-semibold mb-8 text-navy-900 text-shadow\",\n                                children: [\n                                    \"Master the Art of\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-burgundy-700 italic relative\",\n                                        children: [\n                                            \"Magic\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-gold-500 to-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ornamental-divider\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-navy-700 mb-12 max-w-4xl mx-auto font-body leading-relaxed\",\n                                children: \"Discover exclusive tutorials, original techniques, and professional insights from a world-class magician. Elevate your craft with premium educational content steeped in classical tradition and timeless elegance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-primary text-lg\",\n                                        children: \"Explore Tutorials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-outline text-lg\",\n                                        children: \"Watch Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                    className: \"text-lg italic text-navy-600 font-display border-l-4 border-gold-500 pl-6\",\n                                    children: '\"Magic is not about tricks, but about the artistry of wonder and the preservation of mystery.\"'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-cream-100 paper-texture\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"classical-border mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-display font-medium text-center text-navy-900 mb-4\",\n                                    children: \"Featured Content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-navy-600 font-body text-lg max-w-2xl mx-auto\",\n                                    children: \"Explore our carefully curated collection of magical artistry and educational excellence\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card hover-lift\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-display font-medium mb-4 text-burgundy-700\",\n                                            children: \"Latest Portfolio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-navy-700 mb-6 font-body leading-relaxed\",\n                                            children: \"Witness stunning performances and innovative techniques in action, showcasing the artistry and precision of classical magic.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary\",\n                                            children: \"View Portfolio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card hover-lift\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-display font-medium mb-4 text-burgundy-700\",\n                                            children: \"Magic Insights\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-navy-700 mb-6 font-body leading-relaxed\",\n                                            children: \"Deep thoughts on the art, theory, and philosophy of magic, exploring the timeless principles that govern our craft.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary\",\n                                            children: \"Read Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card hover-lift\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-display font-medium mb-4 text-burgundy-700\",\n                                            children: \"Premium Tutorials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-navy-700 mb-6 font-body leading-relaxed\",\n                                            children: \"Exclusive educational content for serious magic practitioners, preserving and teaching the classical methods of the masters.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary\",\n                                            children: \"Start Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"section-padding bg-navy-900 border-t-4 border-gold-600 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 text-4xl text-gold-400\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 right-10 text-4xl text-gold-400 rotate-180\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200\",\n                                children: \"◆\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-max text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"section-divider mb-12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-2xl mr-3\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-display font-semibold text-cream-100 tracking-wide\",\n                                        children: \"Magic Academy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-2xl ml-3 rotate-180\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic\",\n                                children: '\"Preserving the classical traditions of magical artistry for future generations, where timeless elegance meets the wonder of the impossible.\"'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 sm:gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#home\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#portfolio\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#blog\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#tutorials\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Tutorials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-navy-700 pt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-navy-400 font-body\",\n                                    children: \"\\xa9 2025 Magic Academy. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();