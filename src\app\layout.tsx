import type { Metadata } from 'next'
import { Inter, Playfair_Display, Crimson_Text } from 'next/font/google'
import '../styles/globals.css'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
})

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
  weight: ['400', '500', '600', '700'],
})

const crimson = Crimson_Text({
  subsets: ['latin'],
  variable: '--font-crimson',
  weight: ['400', '600'],
})

export const metadata: Metadata = {
  title: 'Magic Academy - Professional Magic Tutorials',
  description: 'Learn advanced magic techniques from a professional magician. Exclusive tutorials, insights, and original card magic methods.',
  keywords: ['magic', 'tutorials', 'card magic', 'professional magic', 'magic lessons'],
  authors: [{ name: 'Magic Academy' }],
  openGraph: {
    title: 'Magic Academy - Professional Magic Tutorials',
    description: 'Learn advanced magic techniques from a professional magician.',
    type: 'website',
    locale: 'en_US',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable} ${crimson.variable}`}>
      <body className="antialiased">
        {children}
      </body>
    </html>
  )
}
