import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { Tutorial } from '@/types'

// 临时示例数据，稍后会从数据文件中读取
const tutorials: Tutorial[] = [
  {
    id: '1',
    title: 'The Classical Card Force',
    description: 'Master the most elegant and deceptive card force techniques used by professional magicians for over a century.',
    price: 49.99,
    coverImage: '/images/tutorials/card-force.jpg',
    videos: [],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    title: 'Advanced Sleight of Hand',
    description: 'Develop lightning-fast finger dexterity and learn the secret moves that separate amateurs from professionals.',
    price: 79.99,
    coverImage: '/images/tutorials/sleight-of-hand.jpg',
    videos: [],
    createdAt: new Date('2024-02-20'),
    updatedAt: new Date('2024-02-20')
  },
  {
    id: '3',
    title: 'Mentalism Fundamentals',
    description: 'Unlock the mysteries of the mind with psychological techniques and methods for creating impossible predictions.',
    price: 59.99,
    coverImage: '/images/tutorials/mentalism.jpg',
    videos: [],
    createdAt: new Date('2024-03-10'),
    updatedAt: new Date('2024-03-10')
  },
  {
    id: '4',
    title: 'The Art of Misdirection',
    description: 'Learn the subtle psychological principles that allow magicians to control attention and create impossible moments.',
    price: 39.99,
    coverImage: '/images/tutorials/misdirection.jpg',
    videos: [],
    createdAt: new Date('2024-04-05'),
    updatedAt: new Date('2024-04-05')
  },
  {
    id: '5',
    title: 'Professional Stage Presence',
    description: 'Transform your performance with commanding stage presence, theatrical timing, and audience connection techniques.',
    price: 69.99,
    coverImage: '/images/tutorials/stage-presence.jpg',
    videos: [],
    createdAt: new Date('2024-05-12'),
    updatedAt: new Date('2024-05-12')
  },
  {
    id: '6',
    title: 'Close-Up Magic Mastery',
    description: 'Perfect your intimate magic skills with advanced techniques for table-side and parlor performances.',
    price: 54.99,
    coverImage: '/images/tutorials/close-up.jpg',
    videos: [],
    createdAt: new Date('2024-06-18'),
    updatedAt: new Date('2024-06-18')
  }
]

export default function TutorialsPage() {
  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12">❦</div>
          <div className="absolute top-20 right-20 text-4xl text-gold-600 -rotate-12">◆</div>
          <div className="absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45">✦</div>
          <div className="absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45">❦</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="classical-border mb-12">
            <h1 className="text-5xl md:text-6xl font-display font-semibold mb-6 text-navy-900 text-shadow">
              Premium Tutorials
            </h1>
            <p className="text-xl md:text-2xl text-navy-700 max-w-3xl mx-auto font-body leading-relaxed">
              Exclusive educational content for serious magic practitioners. Master the classical methods 
              and advanced techniques that separate professionals from amateurs.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Tutorial */}
      {tutorials.length > 0 && (
        <section className="pb-16">
          <div className="container-max">
            <div className="classical-border mb-12 text-center">
              <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
                Featured Tutorial
              </h2>
            </div>
            
            <Link href={`/tutorials/${tutorials[0].id}`} className="group block">
              <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300 max-w-4xl mx-auto">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative">
                    <div className="aspect-video bg-navy-100 rounded-lg overflow-hidden">
                      <div className="w-full h-full bg-gradient-to-br from-burgundy-100 to-navy-100 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-burgundy-800 transition-colors">
                            <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <p className="text-navy-600 font-body">Tutorial Preview</p>
                        </div>
                      </div>
                    </div>
                    <div className="absolute top-4 right-4 bg-gold-600 text-cream-50 px-3 py-1 rounded-full text-sm font-display font-medium">
                      Featured
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-3xl md:text-4xl font-display font-medium mb-4 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {tutorials[0].title}
                    </h3>
                    
                    <p className="text-lg text-navy-700 font-body leading-relaxed mb-6">
                      {tutorials[0].description}
                    </p>
                    
                    <div className="flex items-center justify-between mb-6">
                      <div className="text-3xl font-display font-semibold text-burgundy-700">
                        ${tutorials[0].price}
                      </div>
                      <div className="flex items-center text-navy-600 font-body text-sm">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        2.5 hours of content
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <span className="text-burgundy-600 font-display text-lg group-hover:text-burgundy-800 transition-colors">
                        View Details →
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </section>
      )}

      {/* All Tutorials */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="classical-border mb-12 text-center">
            <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
              All Tutorials
            </h2>
            <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Choose from our complete collection of professional magic education
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tutorials.map((tutorial) => (
              <Link 
                key={tutorial.id} 
                href={`/tutorials/${tutorial.id}`}
                className="group block"
              >
                <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300 h-full">
                  <div className="flex flex-col h-full">
                    {/* Tutorial Cover */}
                    <div className="aspect-video bg-navy-100 rounded-lg overflow-hidden mb-4">
                      <div className="w-full h-full bg-gradient-to-br from-burgundy-100 to-navy-100 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-gold-600 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-gold-700 transition-colors">
                            <svg className="w-5 h-5 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <p className="text-navy-600 font-body text-sm">Preview</p>
                        </div>
                      </div>
                    </div>
                    
                    {/* Tutorial Info */}
                    <div className="flex-grow">
                      <h3 className="text-xl md:text-2xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                        {tutorial.title}
                      </h3>
                      
                      <p className="text-navy-700 font-body leading-relaxed mb-4 flex-grow">
                        {tutorial.description}
                      </p>
                    </div>
                    
                    {/* Tutorial Footer */}
                    <div className="pt-4 border-t border-navy-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl font-display font-semibold text-burgundy-700">
                          ${tutorial.price}
                        </div>
                        <div className="flex items-center text-navy-600 font-body text-sm">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          2-3 hours
                        </div>
                      </div>
                      
                      <div className="text-center">
                        <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                          Learn More →
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding">
        <div className="container-max">
          <div className="max-w-3xl mx-auto text-center">
            <div className="ornamental-divider mb-8"></div>
            <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 text-navy-900">
              Ready to Master Magic?
            </h2>
            <p className="text-lg text-navy-700 mb-8 font-body leading-relaxed">
              Join thousands of magicians who have elevated their craft with our premium tutorials. 
              Each course includes lifetime access, detailed explanations, and professional insights 
              you won't find anywhere else.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/login" className="btn-primary">
                Sign In to Purchase
              </Link>
              <Link href="/portfolio" className="btn-outline">
                View Our Work
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
