{"name": "magic-academy", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@types/bcryptjs": "^2.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/uuid": "^9.0.0", "autoprefixer": "^10.4.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}