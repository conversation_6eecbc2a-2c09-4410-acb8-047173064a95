/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/blog/[slug]/page";
exports.ids = ["app/blog/[slug]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'blog',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/blog/[slug]/page.tsx */ \"(rsc)/./src/app/blog/[slug]/page.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/blog/[slug]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/blog/[slug]/page\",\n        pathname: \"/blog/[slug]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjb2RlJTVDJTVDYXVnJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDY29kZSU1QyU1Q2F1ZyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNOYXZpZ2F0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUE2RjtBQUM3RjtBQUNBLDBLQUFnSCIsInNvdXJjZXMiOlsid2VicGFjazovL21hZ2ljLWFjYWRlbXkvPzUzMTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxjb2RlXFxcXGF1Z1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcY29kZVxcXFxhdWdcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcTmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navigation() {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cream-50/95 backdrop-blur-sm border-b-2 border-navy-200 shadow-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"text-3xl font-display font-semibold text-burgundy-800 tracking-wide relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-600 mr-2\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Magic Academy\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-1 left-8 right-0 h-0.5 bg-gradient-to-r from-burgundy-600 to-transparent opacity-60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Home\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/portfolio\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Portfolio\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/blog\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Blog\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/tutorials\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Tutorials\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/auth/login\",\n                                    className: \"btn-outline ml-4\",\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/portfolio\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/blog\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/tutorials\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Tutorials\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/auth/login\",\n                                className: \"btn-outline inline-block text-center\",\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2e5e0579d49b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFnaWMtYWNhZGVteS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MjMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJlNWUwNTc5ZDQ5YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/blog/[slug]/page.tsx":
/*!**************************************!*\
  !*** ./src/app/blog/[slug]/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(rsc)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(rsc)/./node_modules/remark-gfm/lib/index.js\");\n\n\n\n\n\n\n// 临时示例数据，稍后会从数据文件中读取\nconst blogPosts = [\n    {\n        id: \"1\",\n        title: \"The Philosophy of Wonder: Understanding the True Nature of Magic\",\n        content: `# The Philosophy of Wonder: Understanding the True Nature of Magic\n\nMagic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.\n\n## The Essence of Wonder\n\nWonder is a fundamental human emotion that connects us to the mystery of existence. When we experience true wonder, we are momentarily transported beyond the mundane concerns of daily life into a realm where anything seems possible.\n\n> \"The most beautiful thing we can experience is the mysterious. It is the source of all true art and science.\" - Albert Einstein\n\nThis quote, though not specifically about magic, captures the essence of what we strive to create as magical artists. We are not merely entertainers; we are custodians of mystery, guardians of wonder.\n\n## The Difference Between Tricks and Magic\n\nThere is a profound difference between performing tricks and creating magic:\n\n- **Tricks** are mechanical demonstrations of skill or cleverness\n- **Magic** is the creation of impossible moments that touch the soul\n- **Tricks** impress the mind\n- **Magic** moves the heart\n\n### The Role of the Magician\n\nThe true magician serves as a bridge between the possible and the impossible, the known and the unknown. We do not simply fool people; we invite them to experience a different way of seeing the world.\n\n## Creating Authentic Magical Moments\n\nTo create authentic magical moments, we must:\n\n1. **Understand our audience** - Connect with their hopes, fears, and dreams\n2. **Master our craft** - Technical skill is the foundation upon which artistry is built\n3. **Embrace vulnerability** - True magic happens when we allow ourselves to be genuinely present\n4. **Serve the mystery** - We are not the star; the impossible moment is\n\n## The Responsibility of Wonder\n\nWith the power to create wonder comes great responsibility. We must:\n\n- Never use our abilities to harm or deceive for personal gain\n- Respect the intelligence and dignity of our audiences\n- Preserve the sense of mystery that makes magic possible\n- Pass on our knowledge to worthy successors\n\n## Conclusion\n\nMagic, at its highest form, is a spiritual practice that connects us to the fundamental mystery of existence. When we approach our art with reverence, skill, and genuine care for our audiences, we become part of an ancient tradition that has always sought to remind humanity that there is more to this world than meets the eye.\n\nThe next time you perform, ask yourself: Am I creating a trick, or am I creating magic? The answer will determine not just the quality of your performance, but the depth of its impact on those who witness it.`,\n        excerpt: \"Magic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.\",\n        slug: \"philosophy-of-wonder\",\n        publishedAt: new Date(\"2024-06-15\"),\n        createdAt: new Date(\"2024-06-15\"),\n        updatedAt: new Date(\"2024-06-15\")\n    },\n    {\n        id: \"2\",\n        title: \"The Lost Art of Misdirection: Classical Techniques for Modern Performers\",\n        content: `# The Lost Art of Misdirection: Classical Techniques for Modern Performers\n\nMisdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.\n\n## Understanding True Misdirection\n\nMisdirection is not about forcing attention away from something, but about naturally guiding it toward what you want the audience to see. The best misdirection feels completely natural and unforced.\n\n### The Three Pillars of Classical Misdirection\n\n1. **Physical Misdirection** - Using body language and movement\n2. **Psychological Misdirection** - Leveraging human nature and expectations  \n3. **Temporal Misdirection** - Controlling the timing of attention\n\n## Physical Misdirection Techniques\n\nThe masters understood that the human eye naturally follows movement, particularly the movement of the performer's eyes and hands.\n\n### The Power of Eye Contact\n\nYour eyes are your most powerful tool for misdirection:\n- Where you look, the audience looks\n- Breaking eye contact creates a moment of vulnerability that draws attention\n- Sustained eye contact creates intimacy and trust\n\n### Hand Positioning and Movement\n\nClassical hand positioning follows these principles:\n- Natural gestures feel invisible\n- Unnatural gestures draw attention\n- The hand that moves last is remembered most\n\n## Psychological Misdirection\n\nThis is perhaps the most sophisticated form of misdirection, relying on understanding human psychology rather than physical manipulation.\n\n### Expectation Management\n\nPeople see what they expect to see. By carefully managing expectations, you can:\n- Make the impossible seem natural\n- Hide methods in plain sight\n- Create surprise through violated expectations\n\n### The Principle of Divided Attention\n\nThe human mind can only focus on one thing at a time. By giving the audience something interesting to think about, you can perform your method while their minds are occupied.\n\n## Temporal Misdirection\n\nTiming is everything in misdirection. The classical approach emphasizes:\n\n- **The moment of maximum attention** - When all eyes are on you\n- **The moment of relaxed attention** - When the audience thinks nothing important is happening\n- **The moment of surprise** - When the impossible is revealed\n\n## Modern Applications of Classical Principles\n\nThese timeless principles can be applied to contemporary performance:\n\n### In Close-Up Magic\n- Use conversation to create psychological misdirection\n- Employ natural gestures for physical misdirection\n- Time your methods during moments of laughter or surprise\n\n### In Stage Magic\n- Use lighting and staging for physical misdirection\n- Employ narrative and character for psychological misdirection\n- Control pacing for temporal misdirection\n\n## Common Mistakes in Modern Misdirection\n\nMany contemporary performers make these errors:\n\n1. **Over-directing** - Being too obvious about where they want attention\n2. **Under-motivating** - Not giving the audience a reason to look where they want\n3. **Poor timing** - Rushing the misdirection or holding it too long\n\n## Conclusion\n\nThe art of misdirection is not about deception, but about creating a shared experience of wonder. When done correctly, the audience never feels fooled or manipulated, but rather feels they have participated in something magical.\n\nStudy the masters, practice the principles, and remember: the best misdirection is the misdirection that no one notices.`,\n        excerpt: \"Misdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.\",\n        slug: \"lost-art-misdirection\",\n        publishedAt: new Date(\"2024-05-28\"),\n        createdAt: new Date(\"2024-05-28\"),\n        updatedAt: new Date(\"2024-05-28\")\n    }\n];\nfunction BlogDetailPage({ params }) {\n    const post = blogPosts.find((post)=>post.slug === params.slug);\n    if (!post) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cream-50 text-navy-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-32 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-2 text-sm font-body\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"text-navy-600 hover:text-burgundy-700 transition-colors\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-navy-400\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/blog\",\n                                className: \"text-navy-600 hover:text-burgundy-700 transition-colors\",\n                                children: \"Blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-navy-400\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-burgundy-700 font-medium\",\n                                children: post.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pb-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"classical-border mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl lg:text-6xl font-display font-semibold mb-6 text-navy-900 text-shadow leading-tight\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-6 text-navy-600 font-body mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            post.publishedAt?.toLocaleDateString(\"en-US\", {\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"8 min read\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Philosophy\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-navy-700 font-body leading-relaxed max-w-3xl mx-auto\",\n                                children: post.excerpt\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-cream-100 paper-texture\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"prose prose-lg prose-navy max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                                remarkPlugins: [\n                                    remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                ],\n                                components: {\n                                    h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-5xl font-display font-semibold mb-8 text-navy-900 text-shadow border-b-2 border-gold-200 pb-4\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-display font-medium mb-6 mt-12 text-burgundy-700 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gold-600 mr-3\",\n                                                    children: \"❦\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                children\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-display font-medium mb-4 mt-8 text-navy-800\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-navy-800 font-body leading-relaxed mb-6\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"border-l-4 border-gold-500 pl-6 py-4 my-8 bg-cream-200 rounded-r-lg italic text-lg text-navy-700 font-display\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-none space-y-3 mb-6 text-lg text-navy-800 font-body\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                            className: \"list-decimal list-inside space-y-3 mb-6 text-lg text-navy-800 font-body pl-4\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-burgundy-600 mr-3 mt-1\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            className: \"font-semibold text-burgundy-700\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                            className: \"italic text-navy-700 font-display\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    code: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-navy-100 text-burgundy-700 px-2 py-1 rounded font-mono text-base\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 21\n                                        }, void 0)\n                                },\n                                children: post.content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ornamental-divider mb-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row justify-between items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-navy-600 font-display\",\n                                                children: \"Share this article:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-10 h-10 bg-navy-700 hover:bg-navy-800 text-cream-50 rounded-full flex items-center justify-center transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-10 h-10 bg-navy-700 hover:bg-navy-800 text-cream-50 rounded-full flex items-center justify-center transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-10 h-10 bg-navy-700 hover:bg-navy-800 text-cream-50 rounded-full flex items-center justify-center transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/blog\",\n                                        className: \"btn-outline\",\n                                        children: \"← Back to All Articles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-cream-100 paper-texture\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"classical-border mb-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-navy-600 font-body text-lg max-w-2xl mx-auto\",\n                                    children: \"Continue exploring our collection of magical insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n                            children: blogPosts.filter((relatedPost)=>relatedPost.id !== post.id).slice(0, 2).map((relatedPost)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: `/blog/${relatedPost.slug}`,\n                                    className: \"group block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card hover-lift group-hover:shadow-2xl transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors\",\n                                                children: relatedPost.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-navy-700 font-body leading-relaxed mb-4\",\n                                                children: [\n                                                    relatedPost.excerpt.substring(0, 150),\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-navy-500 font-body\",\n                                                        children: relatedPost.publishedAt?.toLocaleDateString(\"en-US\", {\n                                                            year: \"numeric\",\n                                                            month: \"short\",\n                                                            day: \"numeric\"\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors\",\n                                                        children: \"Read Article →\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 19\n                                    }, this)\n                                }, relatedPost.id, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"section-padding bg-navy-900 border-t-4 border-gold-600 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 text-4xl text-gold-400\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 right-10 text-4xl text-gold-400 rotate-180\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200\",\n                                children: \"◆\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-max text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"section-divider mb-12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-2xl mr-3\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-display font-semibold text-cream-100 tracking-wide\",\n                                        children: \"Magic Academy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-2xl ml-3 rotate-180\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic\",\n                                children: '\"Preserving the classical traditions of magical artistry for future generations, where timeless elegance meets the wonder of the impossible.\"'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 sm:gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/portfolio\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/blog\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/tutorials\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Tutorials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-navy-700 pt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-navy-400 font-body\",\n                                    children: \"\\xa9 2025 Magic Academy. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/blog/[slug]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Crimson_Text\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-crimson\",\"weight\":[\"400\",\"600\"]}],\"variableName\":\"crimson\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Crimson_Text\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-crimson\\\",\\\"weight\\\":[\\\"400\\\",\\\"600\\\"]}],\\\"variableName\\\":\\\"crimson\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Magic Academy - Professional Magic Tutorials\",\n    description: \"Learn advanced magic techniques from a professional magician. Exclusive tutorials, insights, and original card magic methods.\",\n    keywords: [\n        \"magic\",\n        \"tutorials\",\n        \"card magic\",\n        \"professional magic\",\n        \"magic lessons\"\n    ],\n    authors: [\n        {\n            name: \"Magic Academy\"\n        }\n    ],\n    openGraph: {\n        title: \"Magic Academy - Professional Magic Tutorials\",\n        description: \"Learn advanced magic techniques from a professional magician.\",\n        type: \"website\",\n        locale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\code\aug\src\components\Navigation.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2F%5Bslug%5D%2Fpage&page=%2Fblog%2F%5Bslug%5D%2Fpage&appPaths=%2Fblog%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5Bslug%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();