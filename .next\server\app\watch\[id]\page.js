/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/watch/[id]/page";
exports.ids = ["app/watch/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwatch%2F%5Bid%5D%2Fpage&page=%2Fwatch%2F%5Bid%5D%2Fpage&appPaths=%2Fwatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fwatch%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwatch%2F%5Bid%5D%2Fpage&page=%2Fwatch%2F%5Bid%5D%2Fpage&appPaths=%2Fwatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fwatch%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'watch',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/watch/[id]/page.tsx */ \"(rsc)/./src/app/watch/[id]/page.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/watch/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/watch/[id]/page\",\n        pathname: \"/watch/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwatch%2F%5Bid%5D%2Fpage&page=%2Fwatch%2F%5Bid%5D%2Fpage&appPaths=%2Fwatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fwatch%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Capp%5C%5Cwatch%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Capp%5C%5Cwatch%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/watch/[id]/page.tsx */ \"(ssr)/./src/app/watch/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjb2RlJTVDJTVDYXVnJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDd2F0Y2glNUMlNUMlNUJpZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBbUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYWdpYy1hY2FkZW15Lz80YmE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY29kZVxcXFxhdWdcXFxcc3JjXFxcXGFwcFxcXFx3YXRjaFxcXFxbaWRdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Capp%5C%5Cwatch%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/watch/[id]/page.tsx":
/*!*************************************!*\
  !*** ./src/app/watch/[id]/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WatchPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// 模拟用户已购买的教学数据\nconst purchasedTutorials = [\n    {\n        id: \"1\",\n        title: \"The Classical Card Force\",\n        description: \"Master the most elegant and deceptive card force techniques used by professional magicians for over a century.\",\n        price: 49.99,\n        coverImage: \"/images/tutorials/card-force.jpg\",\n        videos: [\n            {\n                id: \"1-1\",\n                title: \"Introduction to Card Forces\",\n                description: \"Understanding the psychology behind forcing cards and why it's the foundation of countless miracles.\",\n                duration: 480,\n                videoUrl: \"/videos/tutorials/card-force-intro.mp4\",\n                thumbnailUrl: \"/images/tutorials/card-force-intro-thumb.jpg\",\n                order: 1,\n                tutorialId: \"1\"\n            },\n            {\n                id: \"1-2\",\n                title: \"The Hindu Force\",\n                description: \"Master the classic Hindu force technique with detailed instruction and common mistakes to avoid.\",\n                duration: 720,\n                videoUrl: \"/videos/tutorials/hindu-force.mp4\",\n                thumbnailUrl: \"/images/tutorials/hindu-force-thumb.jpg\",\n                order: 2,\n                tutorialId: \"1\"\n            },\n            {\n                id: \"1-3\",\n                title: \"The Riffle Force\",\n                description: \"Learn the elegant riffle force and its applications in close-up magic.\",\n                duration: 600,\n                videoUrl: \"/videos/tutorials/riffle-force.mp4\",\n                thumbnailUrl: \"/images/tutorials/riffle-force-thumb.jpg\",\n                order: 3,\n                tutorialId: \"1\"\n            },\n            {\n                id: \"1-4\",\n                title: \"Advanced Applications\",\n                description: \"Combine multiple forces and create your own forcing sequences.\",\n                duration: 900,\n                videoUrl: \"/videos/tutorials/advanced-forces.mp4\",\n                thumbnailUrl: \"/images/tutorials/advanced-forces-thumb.jpg\",\n                order: 4,\n                tutorialId: \"1\"\n            }\n        ],\n        createdAt: new Date(\"2024-01-15\"),\n        updatedAt: new Date(\"2024-01-15\")\n    },\n    {\n        id: \"2\",\n        title: \"Advanced Sleight of Hand\",\n        description: \"Develop lightning-fast finger dexterity and learn the secret moves that separate amateurs from professionals.\",\n        price: 79.99,\n        coverImage: \"/images/tutorials/sleight-of-hand.jpg\",\n        videos: [\n            {\n                id: \"2-1\",\n                title: \"Palm Techniques\",\n                description: \"Master the classic palm and its variations\",\n                duration: 900,\n                videoUrl: \"/videos/tutorials/palm-techniques.mp4\",\n                thumbnailUrl: \"/images/tutorials/palm-techniques-thumb.jpg\",\n                order: 1,\n                tutorialId: \"2\"\n            },\n            {\n                id: \"2-2\",\n                title: \"Advanced Passes\",\n                description: \"Learn the invisible pass and other advanced moves\",\n                duration: 1080,\n                videoUrl: \"/videos/tutorials/advanced-passes.mp4\",\n                thumbnailUrl: \"/images/tutorials/advanced-passes-thumb.jpg\",\n                order: 2,\n                tutorialId: \"2\"\n            }\n        ],\n        createdAt: new Date(\"2024-02-20\"),\n        updatedAt: new Date(\"2024-02-20\")\n    }\n];\nfunction WatchPage({ params }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [currentVideo, setCurrentVideo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const tutorial = purchasedTutorials.find((t)=>t.id === params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // 检查用户是否已登录\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        } else {\n            router.push(\"/auth/login\");\n            return;\n        }\n        // 设置默认播放第一个视频\n        if (tutorial && tutorial.videos.length > 0) {\n            setCurrentVideo(tutorial.videos[0]);\n        }\n        setIsLoading(false);\n    }, [\n        router,\n        tutorial\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cream-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-cream-50\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-navy-600 font-body\",\n                        children: \"Loading tutorial...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    }\n    if (!tutorial || !user) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.notFound)();\n    }\n    const formatDuration = (seconds)=>{\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-navy-900 text-cream-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 w-full z-50 bg-navy-900/95 backdrop-blur-sm border-b border-navy-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/library\",\n                                className: \"flex items-center text-cream-100 hover:text-gold-400 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-display\",\n                                        children: \"Back to Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-display font-medium text-cream-100\",\n                                    children: tutorial.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/tutorials\",\n                                    className: \"text-cream-200 hover:text-gold-400 font-body text-sm transition-colors\",\n                                    children: \"Browse More\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-5xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-black rounded-lg overflow-hidden mb-6 shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-gradient-to-br from-navy-800 to-navy-900 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6 hover:bg-burgundy-800 transition-colors cursor-pointer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-cream-50\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-cream-200 font-body text-lg mb-2\",\n                                                    children: currentVideo?.title || \"Select a video to start learning\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-cream-400 font-body text-sm\",\n                                                    children: \"High-quality video player will be integrated here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                currentVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl md:text-3xl font-display font-medium mb-3 text-cream-100\",\n                                            children: currentVideo.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-cream-300 font-body leading-relaxed mb-4\",\n                                            children: currentVideo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-cream-400 font-body text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Duration: \",\n                                                formatDuration(currentVideo.duration)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8 p-4 bg-navy-800 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const currentIndex = tutorial.videos.findIndex((v)=>v.id === currentVideo?.id);\n                                                if (currentIndex > 0) {\n                                                    setCurrentVideo(tutorial.videos[currentIndex - 1]);\n                                                }\n                                            },\n                                            disabled: !currentVideo || tutorial.videos.findIndex((v)=>v.id === currentVideo.id) === 0,\n                                            className: \"flex items-center text-cream-200 hover:text-cream-100 disabled:text-navy-500 disabled:cursor-not-allowed transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 19l-7-7 7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Previous\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-cream-300 font-body text-sm\",\n                                                children: [\n                                                    currentVideo ? tutorial.videos.findIndex((v)=>v.id === currentVideo.id) + 1 : 0,\n                                                    \" of \",\n                                                    tutorial.videos.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const currentIndex = tutorial.videos.findIndex((v)=>v.id === currentVideo?.id);\n                                                if (currentIndex < tutorial.videos.length - 1) {\n                                                    setCurrentVideo(tutorial.videos[currentIndex + 1]);\n                                                }\n                                            },\n                                            disabled: !currentVideo || tutorial.videos.findIndex((v)=>v.id === currentVideo.id) === tutorial.videos.length - 1,\n                                            className: \"flex items-center text-cream-200 hover:text-cream-100 disabled:text-navy-500 disabled:cursor-not-allowed transition-colors\",\n                                            children: [\n                                                \"Next\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 ml-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-navy-800 border-l border-navy-700 p-6 overflow-y-auto max-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-display font-medium text-cream-100 mb-2\",\n                                        children: \"Course Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-cream-400 font-body text-sm\",\n                                        children: [\n                                            tutorial.videos.length,\n                                            \" lessons • \",\n                                            Math.floor(tutorial.videos.reduce((total, video)=>total + video.duration, 0) / 60),\n                                            \" minutes\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: tutorial.videos.map((video, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentVideo(video),\n                                        className: `w-full text-left p-4 rounded-lg transition-all duration-200 ${currentVideo?.id === video.id ? \"bg-burgundy-700 text-cream-50\" : \"bg-navy-700 text-cream-200 hover:bg-navy-600\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-8 h-8 rounded-full flex items-center justify-center text-xs font-display font-medium ${currentVideo?.id === video.id ? \"bg-cream-100 text-burgundy-700\" : \"bg-navy-600 text-cream-300\"}`,\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-display font-medium mb-1 text-sm leading-tight\",\n                                                            children: video.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-body text-xs opacity-75 mb-2 line-clamp-2\",\n                                                            children: video.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-xs opacity-60\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                formatDuration(video.duration)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, video.id, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-4 bg-navy-700 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-display font-medium text-cream-100 mb-3\",\n                                        children: \"Your Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-cream-300\",\n                                                        children: \"Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-cream-200\",\n                                                        children: [\n                                                            \"2 of \",\n                                                            tutorial.videos.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-navy-600 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-burgundy-600 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: `${2 / tutorial.videos.length * 100}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-cream-400 text-center\",\n                                                children: \"Keep up the great work!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\watch\\\\[id]\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/watch/[id]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4215e67f98d7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFnaWMtYWNhZGVteS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MjMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQyMTVlNjdmOThkN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Crimson_Text\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-crimson\",\"weight\":[\"400\",\"600\"]}],\"variableName\":\"crimson\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Crimson_Text\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-crimson\\\",\\\"weight\\\":[\\\"400\\\",\\\"600\\\"]}],\\\"variableName\\\":\\\"crimson\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Magic Academy - Professional Magic Tutorials\",\n    description: \"Learn advanced magic techniques from a professional magician. Exclusive tutorials, insights, and original card magic methods.\",\n    keywords: [\n        \"magic\",\n        \"tutorials\",\n        \"card magic\",\n        \"professional magic\",\n        \"magic lessons\"\n    ],\n    authors: [\n        {\n            name: \"Magic Academy\"\n        }\n    ],\n    openGraph: {\n        title: \"Magic Academy - Professional Magic Tutorials\",\n        description: \"Learn advanced magic techniques from a professional magician.\",\n        type: \"website\",\n        locale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/watch/[id]/page.tsx":
/*!*************************************!*\
  !*** ./src/app/watch/[id]/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\code\aug\src\app\watch\[id]\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwatch%2F%5Bid%5D%2Fpage&page=%2Fwatch%2F%5Bid%5D%2Fpage&appPaths=%2Fwatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fwatch%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();