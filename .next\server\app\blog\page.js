/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/blog/page";
exports.ids = ["app/blog/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2Fpage&page=%2Fblog%2Fpage&appPaths=%2Fblog%2Fpage&pagePath=private-next-app-dir%2Fblog%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2Fpage&page=%2Fblog%2Fpage&appPaths=%2Fblog%2Fpage&pagePath=private-next-app-dir%2Fblog%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'blog',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/blog/page.tsx */ \"(rsc)/./src/app/blog/page.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/blog/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/blog/page\",\n        pathname: \"/blog\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2Fpage&page=%2Fblog%2Fpage&appPaths=%2Fblog%2Fpage&pagePath=private-next-app-dir%2Fblog%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjb2RlJTVDJTVDYXVnJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDY29kZSU1QyU1Q2F1ZyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNOYXZpZ2F0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUE2RjtBQUM3RjtBQUNBLDBLQUFnSCIsInNvdXJjZXMiOlsid2VicGFjazovL21hZ2ljLWFjYWRlbXkvPzUzMTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxjb2RlXFxcXGF1Z1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcY29kZVxcXFxhdWdcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcTmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Crimson_Text%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-crimson%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22600%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22crimson%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccode%5C%5Caug%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navigation() {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cream-50/95 backdrop-blur-sm border-b-2 border-navy-200 shadow-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"text-3xl font-display font-semibold text-burgundy-800 tracking-wide relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-600 mr-2\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Magic Academy\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-1 left-8 right-0 h-0.5 bg-gradient-to-r from-burgundy-600 to-transparent opacity-60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Home\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/portfolio\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Portfolio\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/blog\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Blog\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/tutorials\",\n                                    className: \"text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group\",\n                                    children: [\n                                        \"Tutorials\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/auth/login\",\n                                    className: \"btn-outline ml-4\",\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/portfolio\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/blog\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/tutorials\",\n                                className: \"text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide\",\n                                children: \"Tutorials\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/auth/login\",\n                                className: \"btn-outline inline-block text-center\",\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2e5e0579d49b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFnaWMtYWNhZGVteS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MjMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJlNWUwNTc5ZDQ5YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/blog/page.tsx":
/*!*******************************!*\
  !*** ./src/app/blog/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n// 临时示例数据，稍后会从数据文件中读取\nconst blogPosts = [\n    {\n        id: \"1\",\n        title: \"The Philosophy of Wonder: Understanding the True Nature of Magic\",\n        content: \"Full content would be here...\",\n        excerpt: \"Magic is not merely about tricks and illusions, but about creating moments of genuine wonder that remind us of the mystery inherent in existence. In this exploration, we delve into the philosophical foundations that separate true magical artistry from mere entertainment.\",\n        slug: \"philosophy-of-wonder\",\n        publishedAt: new Date(\"2024-06-15\"),\n        createdAt: new Date(\"2024-06-15\"),\n        updatedAt: new Date(\"2024-06-15\")\n    },\n    {\n        id: \"2\",\n        title: \"The Lost Art of Misdirection: Classical Techniques for Modern Performers\",\n        content: \"Full content would be here...\",\n        excerpt: \"Misdirection is the cornerstone of magical performance, yet many modern practitioners have lost touch with the subtle, classical approaches that made the masters legendary. This article explores time-tested techniques that create seamless, invisible guidance of attention.\",\n        slug: \"lost-art-misdirection\",\n        publishedAt: new Date(\"2024-05-28\"),\n        createdAt: new Date(\"2024-05-28\"),\n        updatedAt: new Date(\"2024-05-28\")\n    },\n    {\n        id: \"3\",\n        title: \"Building Emotional Connection Through Magic: The Psychology of Performance\",\n        content: \"Full content would be here...\",\n        excerpt: \"The most memorable magical experiences transcend the visual spectacle to create deep emotional connections between performer and audience. Understanding the psychological principles behind this connection is essential for any serious practitioner.\",\n        slug: \"emotional-connection-magic\",\n        publishedAt: new Date(\"2024-05-10\"),\n        createdAt: new Date(\"2024-05-10\"),\n        updatedAt: new Date(\"2024-05-10\")\n    },\n    {\n        id: \"4\",\n        title: \"The Renaissance of Card Magic: Why Classical Methods Still Matter\",\n        content: \"Full content would be here...\",\n        excerpt: \"In an age of digital effects and high-tech illusions, the humble deck of cards remains one of the most powerful tools in a magician's arsenal. This piece examines why classical card techniques continue to captivate audiences and how they can be adapted for contemporary performance.\",\n        slug: \"renaissance-card-magic\",\n        publishedAt: new Date(\"2024-04-22\"),\n        createdAt: new Date(\"2024-04-22\"),\n        updatedAt: new Date(\"2024-04-22\")\n    },\n    {\n        id: \"5\",\n        title: \"The Ethics of Deception: Moral Considerations in Magical Performance\",\n        content: \"Full content would be here...\",\n        excerpt: \"Magic inherently involves deception, but this raises important ethical questions about the nature of our art. How do we balance the need for mystery with honesty? This thoughtful examination explores the moral framework that guides responsible magical practice.\",\n        slug: \"ethics-of-deception\",\n        publishedAt: new Date(\"2024-04-05\"),\n        createdAt: new Date(\"2024-04-05\"),\n        updatedAt: new Date(\"2024-04-05\")\n    },\n    {\n        id: \"6\",\n        title: \"Preserving Magic History: The Importance of Studying the Masters\",\n        content: \"Full content would be here...\",\n        excerpt: \"Every generation of magicians stands on the shoulders of those who came before. By studying the techniques, philosophies, and innovations of magical masters throughout history, we not only improve our own craft but help preserve the rich heritage of our art.\",\n        slug: \"preserving-magic-history\",\n        publishedAt: new Date(\"2024-03-18\"),\n        createdAt: new Date(\"2024-03-18\"),\n        updatedAt: new Date(\"2024-03-18\")\n    }\n];\nfunction BlogPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cream-50 text-navy-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-32 pb-16 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 right-20 text-4xl text-gold-600 -rotate-12\",\n                                children: \"◆\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45\",\n                                children: \"✦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-max text-center relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"classical-border mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-6xl font-display font-semibold mb-6 text-navy-900 text-shadow\",\n                                    children: \"Magic Insights\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-navy-700 max-w-3xl mx-auto font-body leading-relaxed\",\n                                    children: \"Deep thoughts on the art, theory, and philosophy of magic, exploring the timeless principles that govern our craft and the profound connections between performer and audience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            blogPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"classical-border mb-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4\",\n                                children: \"Featured Article\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: `/blog/${blogPosts[0].slug}`,\n                            className: \"group block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card hover-lift group-hover:shadow-2xl transition-all duration-300 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-burgundy-800 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-cream-50\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl md:text-4xl font-display font-medium mb-4 text-burgundy-700 group-hover:text-burgundy-800 transition-colors text-center\",\n                                        children: blogPosts[0].title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-4 text-navy-600 font-body mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    blogPosts[0].publishedAt?.toLocaleDateString(\"en-US\", {\n                                                        year: \"numeric\",\n                                                        month: \"long\",\n                                                        day: \"numeric\"\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"5 min read\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-navy-700 font-body leading-relaxed mb-6 text-center max-w-3xl mx-auto\",\n                                        children: blogPosts[0].excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-burgundy-600 font-display text-lg group-hover:text-burgundy-800 transition-colors\",\n                                            children: \"Read Full Article →\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding bg-cream-100 paper-texture\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"classical-border mb-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4\",\n                                    children: \"All Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-navy-600 font-body text-lg max-w-2xl mx-auto\",\n                                    children: \"Explore our complete collection of magical insights and philosophical reflections\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-8 lg:gap-10\",\n                            children: blogPosts.slice(1).map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: `/blog/${post.slug}`,\n                                    className: \"group block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                        className: \"card hover-lift group-hover:shadow-2xl transition-all duration-300 h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gold-600 rounded-full flex items-center justify-center mb-4 group-hover:bg-gold-700 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-cream-50\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl md:text-2xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors\",\n                                                    children: post.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-navy-600 font-body text-sm mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                post.publishedAt?.toLocaleDateString(\"en-US\", {\n                                                                    year: \"numeric\",\n                                                                    month: \"short\",\n                                                                    day: \"numeric\"\n                                                                })\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 mr-1\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"4 min read\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-navy-700 font-body leading-relaxed mb-4 flex-grow\",\n                                                    children: post.excerpt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 border-t border-navy-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors\",\n                                                        children: \"Continue Reading →\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, post.id, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ornamental-divider mb-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-display font-medium mb-6 text-navy-900\",\n                                children: \"Stay Connected\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-navy-700 mb-8 font-body leading-relaxed\",\n                                children: \"Subscribe to receive new articles and insights directly in your inbox. Join our community of thoughtful magical practitioners.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Enter your email\",\n                                        className: \"input-field flex-grow\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-primary\",\n                                        children: \"Subscribe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"section-padding bg-navy-900 border-t-4 border-gold-600 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 text-4xl text-gold-400\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 right-10 text-4xl text-gold-400 rotate-180\",\n                                children: \"❦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200\",\n                                children: \"◆\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-max text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"section-divider mb-12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-2xl mr-3\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-display font-semibold text-cream-100 tracking-wide\",\n                                        children: \"Magic Academy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-400 text-2xl ml-3 rotate-180\",\n                                        children: \"❦\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic\",\n                                children: '\"Preserving the classical traditions of magical artistry for future generations, where timeless elegance meets the wonder of the impossible.\"'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 sm:gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/portfolio\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/blog\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/tutorials\",\n                                        className: \"text-navy-400 hover:text-gold-400 transition-colors font-display\",\n                                        children: \"Tutorials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-navy-700 pt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-navy-400 font-body\",\n                                    children: \"\\xa9 2025 Magic Academy. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\blog\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Jsb2cvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWdEO0FBQ3BCO0FBRzVCLHFCQUFxQjtBQUNyQixNQUFNRSxZQUF3QjtJQUM1QjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLE1BQU07UUFDTkMsYUFBYSxJQUFJQyxLQUFLO1FBQ3RCQyxXQUFXLElBQUlELEtBQUs7UUFDcEJFLFdBQVcsSUFBSUYsS0FBSztJQUN0QjtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxhQUFhLElBQUlDLEtBQUs7UUFDdEJDLFdBQVcsSUFBSUQsS0FBSztRQUNwQkUsV0FBVyxJQUFJRixLQUFLO0lBQ3RCO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLGFBQWEsSUFBSUMsS0FBSztRQUN0QkMsV0FBVyxJQUFJRCxLQUFLO1FBQ3BCRSxXQUFXLElBQUlGLEtBQUs7SUFDdEI7SUFDQTtRQUNFTixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLE1BQU07UUFDTkMsYUFBYSxJQUFJQyxLQUFLO1FBQ3RCQyxXQUFXLElBQUlELEtBQUs7UUFDcEJFLFdBQVcsSUFBSUYsS0FBSztJQUN0QjtJQUNBO1FBQ0VOLElBQUk7UUFDSkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxhQUFhLElBQUlDLEtBQUs7UUFDdEJDLFdBQVcsSUFBSUQsS0FBSztRQUNwQkUsV0FBVyxJQUFJRixLQUFLO0lBQ3RCO0lBQ0E7UUFDRU4sSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLGFBQWEsSUFBSUMsS0FBSztRQUN0QkMsV0FBVyxJQUFJRCxLQUFLO1FBQ3BCRSxXQUFXLElBQUlGLEtBQUs7SUFDdEI7Q0FDRDtBQUVjLFNBQVNHO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ2QsOERBQVVBOzs7OzswQkFHWCw4REFBQ2U7Z0JBQVFELFdBQVU7O2tDQUVqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0Q7Ozs7OzswQ0FDOUUsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUE2RDs7Ozs7OzBDQUM1RSw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQThEOzs7Ozs7MENBQzdFLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBb0U7Ozs7Ozs7Ozs7OztrQ0FHckYsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFHRixXQUFVOzhDQUFpRjs7Ozs7OzhDQUcvRiw4REFBQ0c7b0NBQUVILFdBQVU7OENBQWdGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNsR1osVUFBVWdCLE1BQU0sR0FBRyxtQkFDbEIsOERBQUNIO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0s7Z0NBQUdMLFdBQVU7MENBQW1FOzs7Ozs7Ozs7OztzQ0FLbkYsOERBQUNiLGlEQUFJQTs0QkFBQ21CLE1BQU0sQ0FBQyxNQUFNLEVBQUVsQixTQUFTLENBQUMsRUFBRSxDQUFDSyxJQUFJLENBQUMsQ0FBQzs0QkFBRU8sV0FBVTtzQ0FDbEQsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDTztnREFBSVAsV0FBVTtnREFBd0JRLE1BQUs7Z0RBQU9DLFFBQU87Z0RBQWVDLFNBQVE7MERBQy9FLDRFQUFDQztvREFBS0MsZUFBYztvREFBUUMsZ0JBQWU7b0RBQVFDLGFBQWE7b0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLM0UsOERBQUNDO3dDQUFHaEIsV0FBVTtrREFDWFosU0FBUyxDQUFDLEVBQUUsQ0FBQ0UsS0FBSzs7Ozs7O2tEQUdyQiw4REFBQ1M7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDaUI7Z0RBQUtqQixXQUFVOztrRUFDZCw4REFBQ087d0RBQUlQLFdBQVU7d0RBQWVRLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQ3RFLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUdDLEdBQUU7Ozs7Ozs7Ozs7O29EQUV0RTNCLFNBQVMsQ0FBQyxFQUFFLENBQUNNLFdBQVcsRUFBRXdCLG1CQUFtQixTQUFTO3dEQUNyREMsTUFBTTt3REFDTkMsT0FBTzt3REFDUEMsS0FBSztvREFDUDs7Ozs7OzswREFFRiw4REFBQ0o7Z0RBQUtqQixXQUFVOztrRUFDZCw4REFBQ087d0RBQUlQLFdBQVU7d0RBQWVRLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQ3RFLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUdDLEdBQUU7Ozs7Ozs7Ozs7O29EQUNqRTs7Ozs7Ozs7Ozs7OztrREFLViw4REFBQ1o7d0NBQUVILFdBQVU7a0RBQ1ZaLFNBQVMsQ0FBQyxFQUFFLENBQUNJLE9BQU87Ozs7OztrREFHdkIsOERBQUNPO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDaUI7NENBQUtqQixXQUFVO3NEQUF5Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVdySCw4REFBQ0M7Z0JBQVFELFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0s7b0NBQUdMLFdBQVU7OENBQW1FOzs7Ozs7OENBR2pGLDhEQUFDRztvQ0FBRUgsV0FBVTs4Q0FBb0Q7Ozs7Ozs7Ozs7OztzQ0FLbkUsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaWixVQUFVa0MsS0FBSyxDQUFDLEdBQUdDLEdBQUcsQ0FBQyxDQUFDQyxxQkFDdkIsOERBQUNyQyxpREFBSUE7b0NBRUhtQixNQUFNLENBQUMsTUFBTSxFQUFFa0IsS0FBSy9CLElBQUksQ0FBQyxDQUFDO29DQUMxQk8sV0FBVTs4Q0FFViw0RUFBQ3lCO3dDQUFRekIsV0FBVTtrREFDakIsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDTzs0REFBSVAsV0FBVTs0REFBd0JRLE1BQUs7NERBQU9DLFFBQU87NERBQWVDLFNBQVE7c0VBQy9FLDRFQUFDQztnRUFBS0MsZUFBYztnRUFBUUMsZ0JBQWU7Z0VBQVFDLGFBQWE7Z0VBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLM0UsOERBQUNDO29EQUFHaEIsV0FBVTs4REFDWHdCLEtBQUtsQyxLQUFLOzs7Ozs7OERBR2IsOERBQUNTO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2lCOzREQUFLakIsV0FBVTs7OEVBQ2QsOERBQUNPO29FQUFJUCxXQUFVO29FQUFlUSxNQUFLO29FQUFPQyxRQUFPO29FQUFlQyxTQUFROzhFQUN0RSw0RUFBQ0M7d0VBQUtDLGVBQWM7d0VBQVFDLGdCQUFlO3dFQUFRQyxhQUFhO3dFQUFHQyxHQUFFOzs7Ozs7Ozs7OztnRUFFdEVTLEtBQUs5QixXQUFXLEVBQUV3QixtQkFBbUIsU0FBUztvRUFDN0NDLE1BQU07b0VBQ05DLE9BQU87b0VBQ1BDLEtBQUs7Z0VBQ1A7Ozs7Ozs7c0VBRUYsOERBQUNKOzREQUFLakIsV0FBVTs7OEVBQ2QsOERBQUNPO29FQUFJUCxXQUFVO29FQUFlUSxNQUFLO29FQUFPQyxRQUFPO29FQUFlQyxTQUFROzhFQUN0RSw0RUFBQ0M7d0VBQUtDLGVBQWM7d0VBQVFDLGdCQUFlO3dFQUFRQyxhQUFhO3dFQUFHQyxHQUFFOzs7Ozs7Ozs7OztnRUFDakU7Ozs7Ozs7Ozs7Ozs7OERBS1YsOERBQUNaO29EQUFFSCxXQUFVOzhEQUNWd0IsS0FBS2hDLE9BQU87Ozs7Ozs4REFHZiw4REFBQ087b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNpQjt3REFBS2pCLFdBQVU7a0VBQXlGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQTFDMUd3QixLQUFLbkMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQXVEdEIsOERBQUNZO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0s7Z0NBQUdMLFdBQVU7MENBQW1FOzs7Ozs7MENBR2pGLDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FBdUQ7Ozs7OzswQ0FJcEUsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzBCO3dDQUNDQyxNQUFLO3dDQUNMQyxhQUFZO3dDQUNaNUIsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDNkI7d0NBQU83QixXQUFVO2tEQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVN4Qyw4REFBQzhCO2dCQUFPOUIsV0FBVTs7a0NBQ2hCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUFpRDs7Ozs7OzBDQUNoRSw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQTZEOzs7Ozs7MENBQzVFLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBaUY7Ozs7Ozs7Ozs7OztrQ0FHbEcsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDaUI7d0NBQUtqQixXQUFVO2tEQUE4Qjs7Ozs7O2tEQUM5Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQW1FOzs7Ozs7a0RBR2xGLDhEQUFDaUI7d0NBQUtqQixXQUFVO2tEQUF5Qzs7Ozs7Ozs7Ozs7OzBDQUUzRCw4REFBQ0c7Z0NBQUVILFdBQVU7MENBQWdFOzs7Ozs7MENBSTdFLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNiLGlEQUFJQTt3Q0FBQ21CLE1BQUs7d0NBQUlOLFdBQVU7a0RBQW1FOzs7Ozs7a0RBQzVGLDhEQUFDYixpREFBSUE7d0NBQUNtQixNQUFLO3dDQUFhTixXQUFVO2tEQUFtRTs7Ozs7O2tEQUNyRyw4REFBQ2IsaURBQUlBO3dDQUFDbUIsTUFBSzt3Q0FBUU4sV0FBVTtrREFBbUU7Ozs7OztrREFDaEcsOERBQUNiLGlEQUFJQTt3Q0FBQ21CLE1BQUs7d0NBQWFOLFdBQVU7a0RBQW1FOzs7Ozs7Ozs7Ozs7MENBRXZHLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0c7b0NBQUVILFdBQVU7OENBQTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFuRCIsInNvdXJjZXMiOlsid2VicGFjazovL21hZ2ljLWFjYWRlbXkvLi9zcmMvYXBwL2Jsb2cvcGFnZS50c3g/ODc4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmF2aWdhdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvTmF2aWdhdGlvbidcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEJsb2dQb3N0IH0gZnJvbSAnQC90eXBlcydcblxuLy8g5Li05pe256S65L6L5pWw5o2u77yM56iN5ZCO5Lya5LuO5pWw5o2u5paH5Lu25Lit6K+75Y+WXG5jb25zdCBibG9nUG9zdHM6IEJsb2dQb3N0W10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHRpdGxlOiAnVGhlIFBoaWxvc29waHkgb2YgV29uZGVyOiBVbmRlcnN0YW5kaW5nIHRoZSBUcnVlIE5hdHVyZSBvZiBNYWdpYycsXG4gICAgY29udGVudDogJ0Z1bGwgY29udGVudCB3b3VsZCBiZSBoZXJlLi4uJyxcbiAgICBleGNlcnB0OiAnTWFnaWMgaXMgbm90IG1lcmVseSBhYm91dCB0cmlja3MgYW5kIGlsbHVzaW9ucywgYnV0IGFib3V0IGNyZWF0aW5nIG1vbWVudHMgb2YgZ2VudWluZSB3b25kZXIgdGhhdCByZW1pbmQgdXMgb2YgdGhlIG15c3RlcnkgaW5oZXJlbnQgaW4gZXhpc3RlbmNlLiBJbiB0aGlzIGV4cGxvcmF0aW9uLCB3ZSBkZWx2ZSBpbnRvIHRoZSBwaGlsb3NvcGhpY2FsIGZvdW5kYXRpb25zIHRoYXQgc2VwYXJhdGUgdHJ1ZSBtYWdpY2FsIGFydGlzdHJ5IGZyb20gbWVyZSBlbnRlcnRhaW5tZW50LicsXG4gICAgc2x1ZzogJ3BoaWxvc29waHktb2Ytd29uZGVyJyxcbiAgICBwdWJsaXNoZWRBdDogbmV3IERhdGUoJzIwMjQtMDYtMTUnKSxcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTA2LTE1JyksXG4gICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgnMjAyNC0wNi0xNScpXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIHRpdGxlOiAnVGhlIExvc3QgQXJ0IG9mIE1pc2RpcmVjdGlvbjogQ2xhc3NpY2FsIFRlY2huaXF1ZXMgZm9yIE1vZGVybiBQZXJmb3JtZXJzJyxcbiAgICBjb250ZW50OiAnRnVsbCBjb250ZW50IHdvdWxkIGJlIGhlcmUuLi4nLFxuICAgIGV4Y2VycHQ6ICdNaXNkaXJlY3Rpb24gaXMgdGhlIGNvcm5lcnN0b25lIG9mIG1hZ2ljYWwgcGVyZm9ybWFuY2UsIHlldCBtYW55IG1vZGVybiBwcmFjdGl0aW9uZXJzIGhhdmUgbG9zdCB0b3VjaCB3aXRoIHRoZSBzdWJ0bGUsIGNsYXNzaWNhbCBhcHByb2FjaGVzIHRoYXQgbWFkZSB0aGUgbWFzdGVycyBsZWdlbmRhcnkuIFRoaXMgYXJ0aWNsZSBleHBsb3JlcyB0aW1lLXRlc3RlZCB0ZWNobmlxdWVzIHRoYXQgY3JlYXRlIHNlYW1sZXNzLCBpbnZpc2libGUgZ3VpZGFuY2Ugb2YgYXR0ZW50aW9uLicsXG4gICAgc2x1ZzogJ2xvc3QtYXJ0LW1pc2RpcmVjdGlvbicsXG4gICAgcHVibGlzaGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTA1LTI4JyksXG4gICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgnMjAyNC0wNS0yOCcpLFxuICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoJzIwMjQtMDUtMjgnKVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICB0aXRsZTogJ0J1aWxkaW5nIEVtb3Rpb25hbCBDb25uZWN0aW9uIFRocm91Z2ggTWFnaWM6IFRoZSBQc3ljaG9sb2d5IG9mIFBlcmZvcm1hbmNlJyxcbiAgICBjb250ZW50OiAnRnVsbCBjb250ZW50IHdvdWxkIGJlIGhlcmUuLi4nLFxuICAgIGV4Y2VycHQ6ICdUaGUgbW9zdCBtZW1vcmFibGUgbWFnaWNhbCBleHBlcmllbmNlcyB0cmFuc2NlbmQgdGhlIHZpc3VhbCBzcGVjdGFjbGUgdG8gY3JlYXRlIGRlZXAgZW1vdGlvbmFsIGNvbm5lY3Rpb25zIGJldHdlZW4gcGVyZm9ybWVyIGFuZCBhdWRpZW5jZS4gVW5kZXJzdGFuZGluZyB0aGUgcHN5Y2hvbG9naWNhbCBwcmluY2lwbGVzIGJlaGluZCB0aGlzIGNvbm5lY3Rpb24gaXMgZXNzZW50aWFsIGZvciBhbnkgc2VyaW91cyBwcmFjdGl0aW9uZXIuJyxcbiAgICBzbHVnOiAnZW1vdGlvbmFsLWNvbm5lY3Rpb24tbWFnaWMnLFxuICAgIHB1Ymxpc2hlZEF0OiBuZXcgRGF0ZSgnMjAyNC0wNS0xMCcpLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoJzIwMjQtMDUtMTAnKSxcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTA1LTEwJylcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgdGl0bGU6ICdUaGUgUmVuYWlzc2FuY2Ugb2YgQ2FyZCBNYWdpYzogV2h5IENsYXNzaWNhbCBNZXRob2RzIFN0aWxsIE1hdHRlcicsXG4gICAgY29udGVudDogJ0Z1bGwgY29udGVudCB3b3VsZCBiZSBoZXJlLi4uJyxcbiAgICBleGNlcnB0OiAnSW4gYW4gYWdlIG9mIGRpZ2l0YWwgZWZmZWN0cyBhbmQgaGlnaC10ZWNoIGlsbHVzaW9ucywgdGhlIGh1bWJsZSBkZWNrIG9mIGNhcmRzIHJlbWFpbnMgb25lIG9mIHRoZSBtb3N0IHBvd2VyZnVsIHRvb2xzIGluIGEgbWFnaWNpYW5cXCdzIGFyc2VuYWwuIFRoaXMgcGllY2UgZXhhbWluZXMgd2h5IGNsYXNzaWNhbCBjYXJkIHRlY2huaXF1ZXMgY29udGludWUgdG8gY2FwdGl2YXRlIGF1ZGllbmNlcyBhbmQgaG93IHRoZXkgY2FuIGJlIGFkYXB0ZWQgZm9yIGNvbnRlbXBvcmFyeSBwZXJmb3JtYW5jZS4nLFxuICAgIHNsdWc6ICdyZW5haXNzYW5jZS1jYXJkLW1hZ2ljJyxcbiAgICBwdWJsaXNoZWRBdDogbmV3IERhdGUoJzIwMjQtMDQtMjInKSxcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTA0LTIyJyksXG4gICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgnMjAyNC0wNC0yMicpXG4gIH0sXG4gIHtcbiAgICBpZDogJzUnLFxuICAgIHRpdGxlOiAnVGhlIEV0aGljcyBvZiBEZWNlcHRpb246IE1vcmFsIENvbnNpZGVyYXRpb25zIGluIE1hZ2ljYWwgUGVyZm9ybWFuY2UnLFxuICAgIGNvbnRlbnQ6ICdGdWxsIGNvbnRlbnQgd291bGQgYmUgaGVyZS4uLicsXG4gICAgZXhjZXJwdDogJ01hZ2ljIGluaGVyZW50bHkgaW52b2x2ZXMgZGVjZXB0aW9uLCBidXQgdGhpcyByYWlzZXMgaW1wb3J0YW50IGV0aGljYWwgcXVlc3Rpb25zIGFib3V0IHRoZSBuYXR1cmUgb2Ygb3VyIGFydC4gSG93IGRvIHdlIGJhbGFuY2UgdGhlIG5lZWQgZm9yIG15c3Rlcnkgd2l0aCBob25lc3R5PyBUaGlzIHRob3VnaHRmdWwgZXhhbWluYXRpb24gZXhwbG9yZXMgdGhlIG1vcmFsIGZyYW1ld29yayB0aGF0IGd1aWRlcyByZXNwb25zaWJsZSBtYWdpY2FsIHByYWN0aWNlLicsXG4gICAgc2x1ZzogJ2V0aGljcy1vZi1kZWNlcHRpb24nLFxuICAgIHB1Ymxpc2hlZEF0OiBuZXcgRGF0ZSgnMjAyNC0wNC0wNScpLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoJzIwMjQtMDQtMDUnKSxcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTA0LTA1JylcbiAgfSxcbiAge1xuICAgIGlkOiAnNicsXG4gICAgdGl0bGU6ICdQcmVzZXJ2aW5nIE1hZ2ljIEhpc3Rvcnk6IFRoZSBJbXBvcnRhbmNlIG9mIFN0dWR5aW5nIHRoZSBNYXN0ZXJzJyxcbiAgICBjb250ZW50OiAnRnVsbCBjb250ZW50IHdvdWxkIGJlIGhlcmUuLi4nLFxuICAgIGV4Y2VycHQ6ICdFdmVyeSBnZW5lcmF0aW9uIG9mIG1hZ2ljaWFucyBzdGFuZHMgb24gdGhlIHNob3VsZGVycyBvZiB0aG9zZSB3aG8gY2FtZSBiZWZvcmUuIEJ5IHN0dWR5aW5nIHRoZSB0ZWNobmlxdWVzLCBwaGlsb3NvcGhpZXMsIGFuZCBpbm5vdmF0aW9ucyBvZiBtYWdpY2FsIG1hc3RlcnMgdGhyb3VnaG91dCBoaXN0b3J5LCB3ZSBub3Qgb25seSBpbXByb3ZlIG91ciBvd24gY3JhZnQgYnV0IGhlbHAgcHJlc2VydmUgdGhlIHJpY2ggaGVyaXRhZ2Ugb2Ygb3VyIGFydC4nLFxuICAgIHNsdWc6ICdwcmVzZXJ2aW5nLW1hZ2ljLWhpc3RvcnknLFxuICAgIHB1Ymxpc2hlZEF0OiBuZXcgRGF0ZSgnMjAyNC0wMy0xOCcpLFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoJzIwMjQtMDMtMTgnKSxcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCcyMDI0LTAzLTE4JylcbiAgfVxuXVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBCbG9nUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1jcmVhbS01MCB0ZXh0LW5hdnktOTAwXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHQtMzIgcGItMTYgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHsvKiBCYWNrZ3JvdW5kIHBhdHRlcm4gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvcGFjaXR5LTVcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xMCBsZWZ0LTEwIHRleHQtNnhsIHRleHQtYnVyZ3VuZHktNzAwIHJvdGF0ZS0xMlwiPuKdpjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIHJpZ2h0LTIwIHRleHQtNHhsIHRleHQtZ29sZC02MDAgLXJvdGF0ZS0xMlwiPuKXhjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIwIGxlZnQtMjAgdGV4dC01eGwgdGV4dC1uYXZ5LTYwMCByb3RhdGUtNDVcIj7inKY8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xMCByaWdodC0xMCB0ZXh0LTN4bCB0ZXh0LWJ1cmd1bmR5LTYwMCAtcm90YXRlLTQ1XCI+4p2mPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLW1heCB0ZXh0LWNlbnRlciByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjbGFzc2ljYWwtYm9yZGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC02eGwgZm9udC1kaXNwbGF5IGZvbnQtc2VtaWJvbGQgbWItNiB0ZXh0LW5hdnktOTAwIHRleHQtc2hhZG93XCI+XG4gICAgICAgICAgICAgIE1hZ2ljIEluc2lnaHRzXG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtZDp0ZXh0LTJ4bCB0ZXh0LW5hdnktNzAwIG1heC13LTN4bCBteC1hdXRvIGZvbnQtYm9keSBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgRGVlcCB0aG91Z2h0cyBvbiB0aGUgYXJ0LCB0aGVvcnksIGFuZCBwaGlsb3NvcGh5IG9mIG1hZ2ljLCBleHBsb3JpbmcgdGhlIHRpbWVsZXNzIHByaW5jaXBsZXMgXG4gICAgICAgICAgICAgIHRoYXQgZ292ZXJuIG91ciBjcmFmdCBhbmQgdGhlIHByb2ZvdW5kIGNvbm5lY3Rpb25zIGJldHdlZW4gcGVyZm9ybWVyIGFuZCBhdWRpZW5jZS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBGZWF0dXJlZCBBcnRpY2xlICovfVxuICAgICAge2Jsb2dQb3N0cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicGItMTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1tYXhcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2xhc3NpY2FsLWJvcmRlciBtYi0xMiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1kaXNwbGF5IGZvbnQtbWVkaXVtIHRleHQtbmF2eS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIEZlYXR1cmVkIEFydGljbGVcbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8TGluayBocmVmPXtgL2Jsb2cvJHtibG9nUG9zdHNbMF0uc2x1Z31gfSBjbGFzc05hbWU9XCJncm91cCBibG9ja1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgaG92ZXItbGlmdCBncm91cC1ob3ZlcjpzaGFkb3ctMnhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctYnVyZ3VuZHktNzAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTQgZ3JvdXAtaG92ZXI6YmctYnVyZ3VuZHktODAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWNyZWFtLTUwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDYuMjUzdjEzbTAtMTNDMTAuODMyIDUuNDc3IDkuMjQ2IDUgNy41IDVTNC4xNjggNS40NzcgMyA2LjI1M3YxM0M0LjE2OCAxOC40NzcgNS43NTQgMTggNy41IDE4czMuMzMyLjQ3NyA0LjUgMS4yNTNtMC0xM0MxMy4xNjggNS40NzcgMTQuNzU0IDUgMTYuNSA1YzEuNzQ2IDAgMy4zMzIuNDc3IDQuNSAxLjI1M3YxM0MxOS44MzIgMTguNDc3IDE4LjI0NiAxOCAxNi41IDE4Yy0xLjc0NiAwLTMuMzMyLjQ3Ny00LjUgMS4yNTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWRpc3BsYXkgZm9udC1tZWRpdW0gbWItNCB0ZXh0LWJ1cmd1bmR5LTcwMCBncm91cC1ob3Zlcjp0ZXh0LWJ1cmd1bmR5LTgwMCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAge2Jsb2dQb3N0c1swXS50aXRsZX1cbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC00IHRleHQtbmF2eS02MDAgZm9udC1ib2R5IG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTggN1YzbTggNFYzbS05IDhoMTBNNSAyMWgxNGEyIDIgMCAwMDItMlY3YTIgMiAwIDAwLTItMkg1YTIgMiAwIDAwLTIgMnYxMmEyIDIgMCAwMDIgMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAge2Jsb2dQb3N0c1swXS5wdWJsaXNoZWRBdD8udG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgXG4gICAgICAgICAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLCBcbiAgICAgICAgICAgICAgICAgICAgICBtb250aDogJ2xvbmcnLCBcbiAgICAgICAgICAgICAgICAgICAgICBkYXk6ICdudW1lcmljJyBcbiAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA4djRsMyAzbTYtM2E5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgNSBtaW4gcmVhZFxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1uYXZ5LTcwMCBmb250LWJvZHkgbGVhZGluZy1yZWxheGVkIG1iLTYgdGV4dC1jZW50ZXIgbWF4LXctM3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIHtibG9nUG9zdHNbMF0uZXhjZXJwdH1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1idXJndW5keS02MDAgZm9udC1kaXNwbGF5IHRleHQtbGcgZ3JvdXAtaG92ZXI6dGV4dC1idXJndW5keS04MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgUmVhZCBGdWxsIEFydGljbGUg4oaSXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG4gICAgICApfVxuXG4gICAgICB7LyogQWxsIEFydGljbGVzICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwic2VjdGlvbi1wYWRkaW5nIGJnLWNyZWFtLTEwMCBwYXBlci10ZXh0dXJlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLW1heFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2xhc3NpY2FsLWJvcmRlciBtYi0xMiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtZGlzcGxheSBmb250LW1lZGl1bSB0ZXh0LW5hdnktOTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgQWxsIEFydGljbGVzXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uYXZ5LTYwMCBmb250LWJvZHkgdGV4dC1sZyBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICBFeHBsb3JlIG91ciBjb21wbGV0ZSBjb2xsZWN0aW9uIG9mIG1hZ2ljYWwgaW5zaWdodHMgYW5kIHBoaWxvc29waGljYWwgcmVmbGVjdGlvbnNcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBnYXAtOCBsZzpnYXAtMTBcIj5cbiAgICAgICAgICAgIHtibG9nUG9zdHMuc2xpY2UoMSkubWFwKChwb3N0KSA9PiAoXG4gICAgICAgICAgICAgIDxMaW5rIFxuICAgICAgICAgICAgICAgIGtleT17cG9zdC5pZH0gXG4gICAgICAgICAgICAgICAgaHJlZj17YC9ibG9nLyR7cG9zdC5zbHVnfWB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgYmxvY2tcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGFydGljbGUgY2xhc3NOYW1lPVwiY2FyZCBob3Zlci1saWZ0IGdyb3VwLWhvdmVyOnNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1nb2xkLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNCBncm91cC1ob3ZlcjpiZy1nb2xkLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtY3JlYW0tNTBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgMTJoNm0tNiA0aDZtMiA1SDdhMiAyIDAgMDEtMi0yVjVhMiAyIDAgMDEyLTJoNS41ODZhMSAxIDAgMDEuNzA3LjI5M2w1LjQxNCA1LjQxNGExIDEgMCAwMS4yOTMuNzA3VjE5YTIgMiAwIDAxLTIgMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgbWQ6dGV4dC0yeGwgZm9udC1kaXNwbGF5IGZvbnQtbWVkaXVtIG1iLTMgdGV4dC1idXJndW5keS03MDAgZ3JvdXAtaG92ZXI6dGV4dC1idXJndW5keS04MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7cG9zdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtbmF2eS02MDAgZm9udC1ib2R5IHRleHQtc20gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOCA3VjNtOCA0VjNtLTkgOGgxME01IDIxaDE0YTIgMiAwIDAwMi0yVjdhMiAyIDAgMDAtMi0ySDVhMiAyIDAgMDAtMiAydjEyYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LnB1Ymxpc2hlZEF0Py50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLCBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbW9udGg6ICdzaG9ydCcsIFxuICAgICAgICAgICAgICAgICAgICAgICAgICBkYXk6ICdudW1lcmljJyBcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zIG1yLTFcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDh2NGwzIDNtNi0zYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDQgbWluIHJlYWRcbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uYXZ5LTcwMCBmb250LWJvZHkgbGVhZGluZy1yZWxheGVkIG1iLTQgZmxleC1ncm93XCI+XG4gICAgICAgICAgICAgICAgICAgICAge3Bvc3QuZXhjZXJwdH1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC00IGJvcmRlci10IGJvcmRlci1uYXZ5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYnVyZ3VuZHktNjAwIGZvbnQtZGlzcGxheSB0ZXh0LXNtIGdyb3VwLWhvdmVyOnRleHQtYnVyZ3VuZHktODAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBDb250aW51ZSBSZWFkaW5nIOKGklxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2FydGljbGU+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIE5ld3NsZXR0ZXIgU2lnbnVwICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwic2VjdGlvbi1wYWRkaW5nXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLW1heFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctMnhsIG14LWF1dG8gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3JuYW1lbnRhbC1kaXZpZGVyIG1iLThcIj48L2Rpdj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWRpc3BsYXkgZm9udC1tZWRpdW0gbWItNiB0ZXh0LW5hdnktOTAwXCI+XG4gICAgICAgICAgICAgIFN0YXkgQ29ubmVjdGVkXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LW5hdnktNzAwIG1iLTggZm9udC1ib2R5IGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICBTdWJzY3JpYmUgdG8gcmVjZWl2ZSBuZXcgYXJ0aWNsZXMgYW5kIGluc2lnaHRzIGRpcmVjdGx5IGluIHlvdXIgaW5ib3guIFxuICAgICAgICAgICAgICBKb2luIG91ciBjb21tdW5pdHkgb2YgdGhvdWdodGZ1bCBtYWdpY2FsIHByYWN0aXRpb25lcnMuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQgbWF4LXctbWQgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8aW5wdXQgXG4gICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCIgXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGVtYWlsXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZCBmbGV4LWdyb3dcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCI+XG4gICAgICAgICAgICAgICAgU3Vic2NyaWJlXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQ2xhc3NpY2FsIEZvb3RlciAqL31cbiAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwic2VjdGlvbi1wYWRkaW5nIGJnLW5hdnktOTAwIGJvcmRlci10LTQgYm9yZGVyLWdvbGQtNjAwIHJlbGF0aXZlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvcGFjaXR5LTVcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xMCBsZWZ0LTEwIHRleHQtNHhsIHRleHQtZ29sZC00MDBcIj7inaY8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xMCByaWdodC0xMCB0ZXh0LTR4bCB0ZXh0LWdvbGQtNDAwIHJvdGF0ZS0xODBcIj7inaY8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xMCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiB0ZXh0LTZ4bCB0ZXh0LWNyZWFtLTIwMFwiPuKXhjwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1tYXggdGV4dC1jZW50ZXIgcmVsYXRpdmUgei0xMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VjdGlvbi1kaXZpZGVyIG1iLTEyXCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNDAwIHRleHQtMnhsIG1yLTNcIj7inaY8L3NwYW4+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtZGlzcGxheSBmb250LXNlbWlib2xkIHRleHQtY3JlYW0tMTAwIHRyYWNraW5nLXdpZGVcIj5cbiAgICAgICAgICAgICAgTWFnaWMgQWNhZGVteVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNDAwIHRleHQtMnhsIG1sLTMgcm90YXRlLTE4MFwiPuKdpjwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW5hdnktMzAwIGZvbnQtYm9keSB0ZXh0LWxnIG1iLTggbWF4LXctMnhsIG14LWF1dG8gaXRhbGljXCI+XG4gICAgICAgICAgICBcIlByZXNlcnZpbmcgdGhlIGNsYXNzaWNhbCB0cmFkaXRpb25zIG9mIG1hZ2ljYWwgYXJ0aXN0cnkgZm9yIGZ1dHVyZSBnZW5lcmF0aW9ucyxcbiAgICAgICAgICAgIHdoZXJlIHRpbWVsZXNzIGVsZWdhbmNlIG1lZXRzIHRoZSB3b25kZXIgb2YgdGhlIGltcG9zc2libGUuXCJcbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBqdXN0aWZ5LWNlbnRlciBnYXAtNCBzbTpnYXAtOCBtYi04XCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtbmF2eS00MDAgaG92ZXI6dGV4dC1nb2xkLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBmb250LWRpc3BsYXlcIj5Ib21lPC9MaW5rPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wb3J0Zm9saW9cIiBjbGFzc05hbWU9XCJ0ZXh0LW5hdnktNDAwIGhvdmVyOnRleHQtZ29sZC00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1kaXNwbGF5XCI+UG9ydGZvbGlvPC9MaW5rPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9ibG9nXCIgY2xhc3NOYW1lPVwidGV4dC1uYXZ5LTQwMCBob3Zlcjp0ZXh0LWdvbGQtNDAwIHRyYW5zaXRpb24tY29sb3JzIGZvbnQtZGlzcGxheVwiPkJsb2c8L0xpbms+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL3R1dG9yaWFsc1wiIGNsYXNzTmFtZT1cInRleHQtbmF2eS00MDAgaG92ZXI6dGV4dC1nb2xkLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBmb250LWRpc3BsYXlcIj5UdXRvcmlhbHM8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItbmF2eS03MDAgcHQtOFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1uYXZ5LTQwMCBmb250LWJvZHlcIj5cbiAgICAgICAgICAgICAgwqkgMjAyNSBNYWdpYyBBY2FkZW15LiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTmF2aWdhdGlvbiIsIkxpbmsiLCJibG9nUG9zdHMiLCJpZCIsInRpdGxlIiwiY29udGVudCIsImV4Y2VycHQiLCJzbHVnIiwicHVibGlzaGVkQXQiLCJEYXRlIiwiY3JlYXRlZEF0IiwidXBkYXRlZEF0IiwiQmxvZ1BhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJzZWN0aW9uIiwiaDEiLCJwIiwibGVuZ3RoIiwiaDIiLCJocmVmIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiaDMiLCJzcGFuIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5Iiwic2xpY2UiLCJtYXAiLCJwb3N0IiwiYXJ0aWNsZSIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwiYnV0dG9uIiwiZm9vdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/blog/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Crimson_Text\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-crimson\",\"weight\":[\"400\",\"600\"]}],\"variableName\":\"crimson\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Crimson_Text\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-crimson\\\",\\\"weight\\\":[\\\"400\\\",\\\"600\\\"]}],\\\"variableName\\\":\\\"crimson\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Magic Academy - Professional Magic Tutorials\",\n    description: \"Learn advanced magic techniques from a professional magician. Exclusive tutorials, insights, and original card magic methods.\",\n    keywords: [\n        \"magic\",\n        \"tutorials\",\n        \"card magic\",\n        \"professional magic\",\n        \"magic lessons\"\n    ],\n    authors: [\n        {\n            name: \"Magic Academy\"\n        }\n    ],\n    openGraph: {\n        title: \"Magic Academy - Professional Magic Tutorials\",\n        description: \"Learn advanced magic techniques from a professional magician.\",\n        type: \"website\",\n        locale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_weight_400_500_600_700_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Crimson_Text_arguments_subsets_latin_variable_font_crimson_weight_400_600_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\code\\\\aug\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\code\aug\src\components\Navigation.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblog%2Fpage&page=%2Fblog%2Fpage&appPaths=%2Fblog%2Fpage&pagePath=private-next-app-dir%2Fblog%2Fpage.tsx&appDir=C%3A%5Ccode%5Caug%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccode%5Caug&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();