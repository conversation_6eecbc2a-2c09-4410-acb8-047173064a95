import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { Tutorial } from '@/types'
import { notFound } from 'next/navigation'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

// 临时示例数据，稍后会从数据文件中读取
const tutorials: Tutorial[] = [
  {
    id: '1',
    title: 'The Classical Card Force',
    description: 'Master the most elegant and deceptive card force techniques used by professional magicians for over a century.',
    price: 49.99,
    coverImage: '/images/tutorials/card-force.jpg',
    videos: [
      {
        id: '1-1',
        title: 'Introduction to Card Forces',
        description: 'Understanding the psychology behind forcing cards',
        duration: 480,
        videoUrl: '/videos/tutorials/card-force-intro.mp4',
        thumbnailUrl: '/images/tutorials/card-force-intro-thumb.jpg',
        order: 1,
        tutorialId: '1'
      },
      {
        id: '1-2',
        title: 'The Hindu Force',
        description: 'Master the classic Hindu force technique',
        duration: 720,
        videoUrl: '/videos/tutorials/hindu-force.mp4',
        thumbnailUrl: '/images/tutorials/hindu-force-thumb.jpg',
        order: 2,
        tutorialId: '1'
      }
    ],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    title: 'Advanced Sleight of Hand',
    description: 'Develop lightning-fast finger dexterity and learn the secret moves that separate amateurs from professionals.',
    price: 79.99,
    coverImage: '/images/tutorials/sleight-of-hand.jpg',
    videos: [],
    createdAt: new Date('2024-02-20'),
    updatedAt: new Date('2024-02-20')
  }
]

// 扩展的产品描述内容（Markdown格式）
const tutorialContent: Record<string, string> = {
  '1': `# The Classical Card Force

Master the most elegant and deceptive card force techniques used by professional magicians for over a century. This comprehensive tutorial covers the fundamental principles and advanced applications of card forcing.

## What You'll Learn

### Core Techniques
- **The Hindu Force** - The most versatile and natural-looking force
- **The Riffle Force** - Perfect for close-up performances
- **The Dribble Force** - Ideal for stage and parlor magic
- **The Cut Force** - Psychological forcing at its finest

### Advanced Applications
- **Multiple Selection Forces** - Force several cards simultaneously
- **Stacked Deck Forces** - Combine with pre-arranged sequences
- **Psychological Forces** - Pure mentalism approaches
- **Recovery Techniques** - What to do when a force fails

## Course Structure

This tutorial is divided into **6 comprehensive modules**, each building upon the previous:

1. **Foundation Principles** (25 minutes)
   - Psychology of forcing
   - Timing and misdirection
   - Common mistakes to avoid

2. **Basic Forces** (35 minutes)
   - Hindu Force variations
   - Riffle Force techniques
   - Practice routines

3. **Advanced Methods** (40 minutes)
   - Dribble Force mastery
   - Cut Force psychology
   - Combination techniques

4. **Performance Applications** (30 minutes)
   - Incorporating forces into routines
   - Audience management
   - Professional tips

5. **Troubleshooting** (20 minutes)
   - Recovery methods
   - Dealing with difficult spectators
   - Backup plans

6. **Master Class** (25 minutes)
   - Advanced psychological principles
   - Creating your own forces
   - Professional insights

## What's Included

- **2.5 hours** of high-definition video instruction
- **Detailed PDF guide** with illustrations and practice exercises
- **Bonus material**: Historical perspectives on card forcing
- **Lifetime access** to all content and future updates
- **Private student forum** access for questions and discussion

## Prerequisites

This course is designed for magicians with:
- Basic card handling skills
- Understanding of fundamental magic principles
- At least 6 months of magic experience

## Instructor Notes

*"The card force is the foundation of countless miracles. Master these techniques, and you'll have the power to create impossible moments that will stay with your audiences forever."*

## Student Testimonials

> "This tutorial completely transformed my card magic. The psychological insights alone are worth the price." - **Michael R., Professional Magician**

> "Clear, detailed instruction with real-world applications. Exactly what I needed to take my magic to the next level." - **Sarah L., Magic Enthusiast**

## Money-Back Guarantee

We're so confident you'll love this tutorial that we offer a **30-day money-back guarantee**. If you're not completely satisfied, we'll refund your purchase, no questions asked.`,

  '2': `# Advanced Sleight of Hand

Develop lightning-fast finger dexterity and learn the secret moves that separate amateurs from professionals. This intensive course covers the most challenging and rewarding aspects of close-up magic.

## Course Overview

This is not a beginner's course. We assume you already have solid fundamentals and are ready to push your skills to the professional level.

### What Makes This Different

- **Slow-motion analysis** of every move
- **Multiple camera angles** for perfect clarity  
- **Professional performance footage** showing real-world applications
- **Detailed practice routines** for skill development

## Techniques Covered

### Palm Techniques
- **Classic Palm variations**
- **Finger Palm mastery**
- **Thumb Palm applications**
- **Multiple selection palming**

### Advanced Passes
- **Invisible Pass**
- **Spring Pass**
- **Diagonal Palm Shift**
- **Center Tear applications**

### Flourishes & Displays
- **One-handed cuts**
- **Aerial displays**
- **Color changes**
- **Transformation sequences**

## Prerequisites

- **2+ years** of serious magic practice
- Mastery of basic sleights (double lift, elmsley count, etc.)
- Comfortable with intermediate card handling
- Dedicated to daily practice

## Investment in Your Craft

At $79.99, this represents incredible value for professional-level instruction that would cost hundreds in private lessons.`
}

interface TutorialDetailPageProps {
  params: {
    id: string
  }
}

export default function TutorialDetailPage({ params }: TutorialDetailPageProps) {
  const tutorial = tutorials.find(t => t.id === params.id)
  
  if (!tutorial) {
    notFound()
  }

  const content = tutorialContent[tutorial.id] || tutorial.description

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Breadcrumb */}
      <section className="pt-32 pb-8">
        <div className="container-max">
          <nav className="flex items-center space-x-2 text-sm font-body">
            <Link href="/" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Home
            </Link>
            <span className="text-navy-400">→</span>
            <Link href="/tutorials" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Tutorials
            </Link>
            <span className="text-navy-400">→</span>
            <span className="text-burgundy-700 font-medium">{tutorial.title}</span>
          </nav>
        </div>
      </section>

      {/* Tutorial Header */}
      <section className="pb-16">
        <div className="container-max">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Tutorial Preview */}
            <div className="relative">
              <div className="aspect-video bg-navy-100 rounded-lg overflow-hidden shadow-2xl">
                <div className="w-full h-full bg-gradient-to-br from-burgundy-100 to-navy-100 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6 hover:bg-burgundy-800 transition-colors cursor-pointer">
                      <svg className="w-8 h-8 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-navy-600 font-body text-lg">Watch Preview</p>
                  </div>
                </div>
              </div>
              
              {/* Video List Preview */}
              {tutorial.videos.length > 0 && (
                <div className="mt-6 bg-cream-100 rounded-lg p-4">
                  <h4 className="font-display font-medium text-navy-800 mb-3">Course Content</h4>
                  <div className="space-y-2">
                    {tutorial.videos.slice(0, 3).map((video) => (
                      <div key={video.id} className="flex items-center text-sm text-navy-600">
                        <svg className="w-4 h-4 mr-2 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="flex-grow">{video.title}</span>
                        <span className="text-navy-500">{Math.floor(video.duration / 60)}min</span>
                      </div>
                    ))}
                    {tutorial.videos.length > 3 && (
                      <div className="text-sm text-navy-500 italic">
                        +{tutorial.videos.length - 3} more videos
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Tutorial Info */}
            <div>
              <div className="classical-border mb-8">
                <h1 className="text-4xl md:text-5xl font-display font-semibold mb-4 text-navy-900 text-shadow">
                  {tutorial.title}
                </h1>
                <p className="text-xl text-navy-700 font-body leading-relaxed">
                  {tutorial.description}
                </p>
              </div>

              {/* Pricing & Purchase */}
              <div className="card mb-8">
                <div className="flex items-center justify-between mb-6">
                  <div className="text-4xl font-display font-semibold text-burgundy-700">
                    ${tutorial.price}
                  </div>
                  <div className="text-right">
                    <div className="text-navy-600 font-body text-sm mb-1">One-time purchase</div>
                    <div className="text-navy-600 font-body text-sm">Lifetime access</div>
                  </div>
                </div>

                <button className="btn-primary w-full text-lg mb-4">
                  Purchase Tutorial
                </button>

                <div className="text-center text-navy-600 font-body text-sm">
                  Secure payment via PayPal • 30-day money-back guarantee
                </div>
              </div>

              {/* Tutorial Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center p-4 bg-cream-100 rounded-lg">
                  <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                    2.5h
                  </div>
                  <div className="text-navy-600 font-body text-sm">Total Duration</div>
                </div>
                <div className="text-center p-4 bg-cream-100 rounded-lg">
                  <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                    {tutorial.videos.length || 6}
                  </div>
                  <div className="text-navy-600 font-body text-sm">Video Lessons</div>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-3">
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Lifetime access to all content
                </div>
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  High-definition video instruction
                </div>
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Downloadable practice materials
                </div>
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  30-day money-back guarantee
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tutorial Content */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="max-w-4xl mx-auto">
            <article className="prose prose-lg prose-navy max-w-none">
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({children}) => (
                    <h1 className="text-4xl md:text-5xl font-display font-semibold mb-8 text-navy-900 text-shadow border-b-2 border-gold-200 pb-4">
                      {children}
                    </h1>
                  ),
                  h2: ({children}) => (
                    <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 mt-12 text-burgundy-700 relative">
                      <span className="text-gold-600 mr-3">❦</span>
                      {children}
                    </h2>
                  ),
                  h3: ({children}) => (
                    <h3 className="text-2xl md:text-3xl font-display font-medium mb-4 mt-8 text-navy-800">
                      {children}
                    </h3>
                  ),
                  p: ({children}) => (
                    <p className="text-lg text-navy-800 font-body leading-relaxed mb-6">
                      {children}
                    </p>
                  ),
                  blockquote: ({children}) => (
                    <blockquote className="border-l-4 border-gold-500 pl-6 py-4 my-8 bg-cream-200 rounded-r-lg italic text-lg text-navy-700 font-display">
                      {children}
                    </blockquote>
                  ),
                  ul: ({children}) => (
                    <ul className="list-none space-y-3 mb-6 text-lg text-navy-800 font-body">
                      {children}
                    </ul>
                  ),
                  ol: ({children}) => (
                    <ol className="list-decimal list-inside space-y-3 mb-6 text-lg text-navy-800 font-body pl-4">
                      {children}
                    </ol>
                  ),
                  li: ({children}) => (
                    <li className="flex items-start">
                      <span className="text-burgundy-600 mr-3 mt-1">•</span>
                      <span>{children}</span>
                    </li>
                  ),
                  strong: ({children}) => (
                    <strong className="font-semibold text-burgundy-700">
                      {children}
                    </strong>
                  ),
                  em: ({children}) => (
                    <em className="italic text-navy-700 font-display">
                      {children}
                    </em>
                  )
                }}
              >
                {content}
              </ReactMarkdown>
            </article>
          </div>
        </div>
      </section>

      {/* Related Tutorials */}
      <section className="section-padding">
        <div className="container-max">
          <div className="classical-border mb-12 text-center">
            <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
              Related Tutorials
            </h2>
            <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Continue your magical education with these complementary courses
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {tutorials
              .filter(relatedTutorial => relatedTutorial.id !== tutorial.id)
              .slice(0, 2)
              .map((relatedTutorial) => (
                <Link 
                  key={relatedTutorial.id} 
                  href={`/tutorials/${relatedTutorial.id}`}
                  className="group block"
                >
                  <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300">
                    <h3 className="text-xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {relatedTutorial.title}
                    </h3>
                    <p className="text-navy-700 font-body leading-relaxed mb-4">
                      {relatedTutorial.description.substring(0, 120)}...
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-display font-semibold text-burgundy-700">
                        ${relatedTutorial.price}
                      </span>
                      <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                        View Tutorial →
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
